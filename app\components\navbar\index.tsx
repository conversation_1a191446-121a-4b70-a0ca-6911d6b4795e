"use client";
// import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { useAuth } from "@/app/context/AuthProvider";
// import { SocialIcon } from "@/app/types/CommonComponent.types";
// import api from "@/app/utils/axiosInstance";
import { RootState } from "@/redux/store";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import {
  UserIcon,
  ChevronDownIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/react/24/outline";
import { TrophyIcon } from "@heroicons/react/24/solid";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
// import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import UsersCountComponent from "@/app/components/usersCountComponent";
const userNavigation = [
  { name: "Your profile", href: "/profile", icon: UserIcon },
  { name: "Logout", href: "/logout", icon: ArrowTopRightOnSquareIcon },
];

const Navbar = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const user = useSelector((state: RootState) => state.user);
  // const [socialIcons, setSocialIcons] = useState<SocialIcon[]>([]);

  const searchParamas = useSearchParams();
  const tournament_id = searchParamas.get("tournament_id");
  const slot_id = searchParamas.get("slot_id");

  // const fetchSocialIcons = async () => {
  //   try {
  //     const response = await api.get(API_ENDPOINTS.SOCIAL_ICONS);
  //     if (response.status === 200) {
  //       setSocialIcons(response?.data);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };
  // useEffect(() => {
  //   fetchSocialIcons();
  // }, []);

  return (
    <div className="bg-[#141517] border-b border-[#c9ff88] sticky top-0 z-10">
      <div className="pr-10 pl-3">
        <div className="relative flex h-[84px] items-center justify-between ">
          <div className="flex items-center justify-start">
            <Link
              href="/"
              className="flex flex-shrink-0 items-center cursor-pointer"
            >
              <Image
                alt="Company Logo"
                src="/images/logo.png"
                width={154}
                height={50}
                priority
              />
            </Link>

            <UsersCountComponent />
          </div>

          {/* {socialIcons && (
            <div className="flex  items-center gap-10">
              {socialIcons.map((icon, index) => (
                <Link
                  href={icon.link}
                  key={index}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src={icon.image} width={40} height={40} alt="" />
                </Link>
              ))}
            </div>
          )} */}

          <div className="flex items-center">
            {isAuthenticated && (
              <div className="flex items-center gap-3">
                <div className="flex gap-5 mr-5">
                  <Link
                    href="/my-wallet"
                    className="flex items-center group gap-2.5 px-5 py-1.5 rounded-full hover:bg-red-600 bg-[#141517] text-red-500 font-semibold text-2xl hover:text-[#131517] border border-red-500 transition-all duration-200"
                  >
                    <TrophyIcon
                      aria-hidden="true"
                      className="h-6 w-6 text-red-500 group-hover:text-[#131517] transition-all duration-200"
                    />
                    <span>₹{user?.redeem_wallet?.toLocaleString("en-IN")}</span>
                  </Link>
                </div>
                <div className="flex gap-5 mr-5">
                  <Link
                    href="/my-wallet"
                    className="flex items-center group gap-2.5 px-5 py-1.5 rounded-full hover:bg-[#c9ff88] bg-[#141517] text-[#c9ff88] font-semibold text-2xl hover:text-[#131517] border border-[#c9ff88] transition-all duration-200"
                  >
                    <Image
                      src="/icons/wallet.png"
                      alt="wallet"
                      width={26}
                      height={26}
                      className="group-hover:grayscale group-hover:brightness-0 grayscale-0 brightness-100 transition-all duration-200"
                    />
                    <span>₹{user?.wallet?.toLocaleString("en-IN")}</span>
                  </Link>
                </div>
              </div>
            )}

            {isAuthenticated === false && (
              <div className="flex gap-5 items-center">
                <button
                  className="text-white font-semibold text-lg hover:bg-[#c9ff88] transition-all duration-200 hover:text-[#131517] border border-white rounded-full px-5 py-1.5"
                  onClick={() =>
                    router.push(
                      `/login${
                        tournament_id && slot_id
                          ? `?tournament_id=${tournament_id}&slot_id=${slot_id}`
                          : ""
                      }`
                    )
                  }
                >
                  Login
                </button>
                <button
                  className="text-white font-semibold text-lg hover:bg-[#c9ff88] transition-all duration-200 hover:text-[#131517] border border-white rounded-full px-5 py-1.5"
                  onClick={() =>
                    router.push(
                      `/signup${
                        tournament_id && slot_id
                          ? `?tournament_id=${tournament_id}&slot_id=${slot_id}`
                          : ""
                      }`
                    )
                  }
                >
                  Signup
                </button>
              </div>
            )}
            {isAuthenticated && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                <div className="flex items-center gap-x-4 lg:gap-x-6">
                  <Menu as="div" className="relative">
                    <MenuButton className="-m-1.5 flex items-center p-1.5">
                      <span className="sr-only">Open user menu</span>
                      <Image
                        alt=""
                        src="/icons/avatar.png"
                        width={48}
                        height={48}
                        className="rounded-full bg-gray-50"
                      />
                      <span className="hidden lg:flex lg:items-center">
                        <span
                          aria-hidden="true"
                          className="ml-4 text-xl font-semibold leading-6 text-white"
                        >
                          {user?.name}
                        </span>
                        <ChevronDownIcon
                          aria-hidden="true"
                          className="ml-2 h-5 w-5 text-white"
                        />
                      </span>
                    </MenuButton>
                    <MenuItems
                      transition
                      className="absolute right-0 z-10 min-w-40 mt-2.5 origin-top-right rounded-md bg-[#141517] py-2 shadow-lg ring-1 ring-gray-900/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                    >
                      {userNavigation.map((item) => (
                        <MenuItem key={item.name}>
                          <div
                            className="flex items-center gap-4 cursor-pointer pl-3 pr-5 py-1 text-base text-[#c9ff88] hover:bg-[#c9ff88] hover:text-[#131517]"
                            onClick={() => router.push(item.href)}
                          >
                            <item.icon className="h-5 w-5" />
                            <span className="">{item.name}</span>
                          </div>
                        </MenuItem>
                      ))}
                    </MenuItems>
                  </Menu>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
