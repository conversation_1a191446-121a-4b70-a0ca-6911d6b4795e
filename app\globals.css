@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom dot styles */
.slick-dots li button:before {
  color: white !important;
  font-size: 12px;
}

.slick-dots li.slick-active button:before {
  color: white !important;
}


@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.blinking-dot {
  width: 1rem; /* Adjust size as needed */
  height: 1rem; /* Adjust size as needed */
  background-color: #4caf50; /* Green color */
  border-radius: 50%;
  animation: blink 1s infinite; /* Adjust duration as needed */
}