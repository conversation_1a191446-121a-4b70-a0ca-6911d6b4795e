"use client";

import localFont from "next/font/local";
import Navbar from "./components/navbar";
import { ToastContainer } from "react-toastify";
import "./globals.css";
import "react-toastify/dist/ReactToastify.css";
import { AuthProvider } from "./context/AuthProvider";
import { Provider } from "react-redux";
import { store } from "@/redux/store";
import Script from "next/script";
import { Suspense } from "react";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { SidebarProvider } from "./context/SidebarContext";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
      <Suspense fallback={<div>Loading...</div>}>
        <ToastContainer />
        <Provider store={store}>
          <AuthProvider>
            <SidebarProvider>
              <div>
                <Navbar />
                <main className="flex-1 bg-gradient-to-r from-[#122531] via-[#121f28] to-[#1a2c38] min-h-[calc(100vh-84px)]">
                  {children}
                </main>
              </div>
            </SidebarProvider>
          </AuthProvider>
        </Provider>
      </Suspense>
      <Script src="https://checkout.razorpay.com/v1/checkout.js" />
    </div>
  );
}
