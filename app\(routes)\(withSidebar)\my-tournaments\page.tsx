"use client";
import { useEffect, useState } from "react";
import {
  MyBookingInfo,
  MybookingsRes,
} from "@/app/types/CommonComponent.types";
import Loader from "@/app/components/common/Loader";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { formatDate } from "@/app/utils/helper";
import { useRouter } from "next/navigation";
import StartGameModal from "@/app/components/startGameModal";
import { Modal } from "@/app/components/modal";
import withAuth from "@/app/utils/withAuth";
import ServerError from "@/app/components/common/ServerError";
import Pagination from "@/app/components/pagination";

const tableHeadings = [
  { id: 1, label: "Booking ID" },
  { id: 2, label: "Booked on" },
  { id: 3, label: "Game Name" },
  { id: 4, label: "Slot" },
  { id: 5, label: "In Game Name" },
  { id: 6, label: "Tournament Link" },
  { id: 7, label: "Result" },
  { id: 8, label: "Amount" },
  { id: 9, label: "Status" },
];

const MyTournamentsPage = () => {
  const [myBookings, setMyBookings] = useState<MybookingsRes>(
    {} as MybookingsRes
  );
  const [isLoading, setIsLoading] = useState(true);
  const [showGameDetailsModal, setShowGameDetailsModal] = useState(false);
  const [startGame, setStartGame] = useState<MyBookingInfo>();
  const [error, setError] = useState<any>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const totalPages = Math.ceil(myBookings?.count / itemsPerPage);
  const router = useRouter();

  const fetchMyTournaments = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.GET_BOOKINGS(currentPage));
      if (response.status === 200) {
        setMyBookings(response?.data);
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error);
    }
  };
  useEffect(() => {
    fetchMyTournaments();
  }, [currentPage]);

  const onRowClick = (bookingId: string) => {
    router.push(`/my-tournaments/${bookingId}`);
  };

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  return (
    <>
      <div className="p-8 w-full">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold text-white">MY TOURNAMENTS</h1>
        </div>
        <div className=" pt-8">
          {isLoading && <Loader />}

          {!isLoading && myBookings?.results?.length === 0 && (
            <p className="text-gray-300 text-lg font-medium">
              No tournaments booked yet
            </p>
          )}

          {myBookings?.results?.length > 0 && (
            <div>
              <table className="min-w-full divide-y divide-gray-700">
                <thead>
                  <tr>
                    {tableHeadings.map((heading) => (
                      <th
                        key={heading.id}
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                      >
                        {heading.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {myBookings?.results?.map((booking) => (
                    <tr
                      key={booking.booking_id}
                      className="hover:bg-[#c9ff88] hover:cursor-pointer hover:text-[#131517] text-gray-300"
                      onClick={() => onRowClick(booking.booking_id)}
                    >
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-base font-medium">
                        {booking?.booking_id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {formatDate(booking?.created_at.split("T")[0])}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {booking?.tournament_name}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {booking?.time_slot},{" "}
                        {formatDate(booking?.tournament_date)}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {booking?.in_game_name ?? "-"}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setStartGame(booking);
                            setShowGameDetailsModal(true);
                          }}
                          disabled={
                            booking.status?.toLowerCase() === "cancelled"
                          }
                          className={`justify-center rounded-md bg-red-600 px-3 py-1.5 text-base font-semibold leading-6 text-white shadow-sm ${
                            booking.status?.toLowerCase() === "cancelled"
                              ? "opacity-50 cursor-not-allowed"
                              : "hover:bg-red-500"
                          }`}
                        >
                          {booking.status?.toLowerCase() === "cancelled"
                            ? "Cancelled"
                            : "Start game"}
                        </button>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {booking?.result ?? "-"}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        ₹
                        {Number(booking?.amount).toLocaleString("en-IN") ?? "-"}
                      </td>
                      <td
                        className={`whitespace-nowrap px-3 py-4 text-base font-semibold ${
                          booking?.status.toLowerCase() === "booked"
                            ? "text-orange-500"
                            : booking?.status.toLowerCase() === "cancelled"
                            ? " text-red-500"
                            : booking?.status.toLowerCase() === "completed"
                            ? " text-green-500"
                            : ""
                        }`}
                      >
                        {booking?.status ?? "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      {myBookings?.count > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      )}
      <Modal
        modalOpen={showGameDetailsModal}
        handleModalOpen={() => setShowGameDetailsModal(false)}
      >
        <StartGameModal
          game_link={startGame?.game_link as string}
          room_id={startGame?.room_id as string}
          room_password={startGame?.room_password as string}
          bookingId={startGame?.booking_id}
          tournamentName={startGame?.tournament_name || ""}
          inGameName={startGame?.in_game_name || ""}
          showUpdateInGameNameForm={true}
          onSuccess={() => fetchMyTournaments()}
        />
      </Modal>
    </>
  );
};

export default withAuth(MyTournamentsPage);
