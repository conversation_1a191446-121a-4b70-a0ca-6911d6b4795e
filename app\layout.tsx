import { Viewport } from "next";
import RootLayout from "./RootLayout";

export const viewport: Viewport = {
  initialScale: 0.1,
  width:"device-width",
};

export const metadata = {
  title: "GamyDay",
  icons: {
    icon: "/favicon.png",
  },
};

export default function ServerRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <RootLayout>{children}</RootLayout>
      </body>
    </html>
  );
}
