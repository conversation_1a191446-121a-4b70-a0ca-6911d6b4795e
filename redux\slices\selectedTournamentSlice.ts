import { TimeSlot, TournamentDetails } from "@/app/types/CommonComponent.types";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface SelectedTournamentState {
  tournamentDetails: TournamentDetails | null;
  selectedTime: TimeSlot | null;
}

const initialState: SelectedTournamentState = {
  tournamentDetails: null,
  selectedTime: null,
};

const selectedTournamentSlice = createSlice({
  name: "selectedTournament",
  initialState,
  reducers: {
    setSelectedTournament: (
      state,
      action: PayloadAction<SelectedTournamentState>
    ) => {
      state.tournamentDetails = action.payload.tournamentDetails;
      state.selectedTime = action.payload.selectedTime;
    },
    clearSelectedTournament: (state) => {
      state.tournamentDetails = null;
      state.selectedTime = null;
    },
  },
});

export const { setSelectedTournament, clearSelectedTournament } =
  selectedTournamentSlice.actions;
export default selectedTournamentSlice.reducer;
