"use client";
import { useEffect, useState } from "react";
import {
  Option,
  RaiseRequestData,
  RaiseRequestRes,
} from "@/app/types/CommonComponent.types";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { Modal } from "@/app/components/modal";
import { formatDate } from "@/app/utils/helper";
import RaiseRequestForm from "@/app/components/raiseRequestForm";
import ViewRaiseRequest from "@/app/components/viewRaiseRequest";
import Loader from "@/app/components/common/Loader";
import withAuth from "@/app/utils/withAuth";
import ServerError from "@/app/components/common/ServerError";
import Pagination from "@/app/components/pagination";

const tableHeadings = [
  { id: 0, label: "Request Id" },
  { id: 1, label: "Issue" },
  { id: 2, label: "Status" },
  { id: 3, label: "Created On" },
];

const RaiseRequestPage = () => {
  const [requestData, setRequestData] = useState<RaiseRequestRes>(
    {} as RaiseRequestRes
  );
  const [options, setOptions] = useState<Option[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showRaiseRequestModal, setShowRaiseRequestModal] = useState(false);
  const [viewRaiseRequestModal, setViewRaiseRequestModal] = useState(false);
  const [error, setError] = useState<any>(null);
  const [selectedRequest, setSelectedRequest] =
    useState<RaiseRequestData | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const totalPages = Math.ceil(requestData?.count / itemsPerPage);

  const onRaiseRequestClick = () => {
    setSelectedRequest(null);
    setShowRaiseRequestModal(true);
  };

  const onViewRequestClick = (request: RaiseRequestData) => {
    setSelectedRequest(request);
    setViewRaiseRequestModal(true);
  };

  const fetchRequestData = async () => {
    try {
      const requestResponse = await api.get(
        API_ENDPOINTS.GET_RAISE_REQUEST(currentPage)
      );
      if (requestResponse.status === 200) {
        setRequestData(requestResponse?.data);
      }
    } catch (error: any) {
      setError(error);
    }
  };

  const fetchOptionsData = async () => {
    try {
      const optionsResponse = await api.get(
        API_ENDPOINTS.RAISE_REQUEST_OPTIONS
      );
      if (optionsResponse.status === 200) {
        setOptions(optionsResponse?.data?.data);
      }
    } catch (error: any) {
      console.log(error);
    }
  };

  const fetchAllData = async () => {
    setIsLoading(true);
    await Promise.all([fetchRequestData(), fetchOptionsData()]);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  useEffect(() => {
    fetchRequestData();
  }, [currentPage]);

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  return (
    <div className="p-8 w-full">
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold text-white">My Requests</h1>
        </div>
        <button
          onClick={onRaiseRequestClick}
          className="flex justify-center rounded-md bg-red-600 px-3 py-1.5 text-base font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
        >
          Raise Request
        </button>
      </div>
      <hr className="my-8" />
      {isLoading && <Loader />}

      {!isLoading && requestData?.results?.length === 0 && (
        <p className="text-gray-300 text-lg font-medium">No request found.</p>
      )}
      {requestData?.results?.length > 0 && (
        <div className="pt-8">
          <table className="min-w-full divide-y divide-gray-700">
            <thead>
              <tr>
                {tableHeadings.map((heading) => (
                  <th
                    key={heading.id}
                    scope="col"
                    className="px-3 py-3.5 text-left text-base font-semibold text-white"
                  >
                    {heading.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-800">
              {requestData?.results?.map((request) => (
                <tr
                  key={request.id}
                  className="hover:bg-[#c9ff88] hover:cursor-pointer hover:text-[#131517] text-gray-300"
                >
                  <td className="whitespace-nowrap px-3 py-4 text-base ">
                    {request.id}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-base ">
                    {request.issue}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-base ">
                    <span
                      className={`font-bold ${
                        request.status?.toLowerCase() === "pending"
                          ? "text-orange-500"
                          : request.status?.toLowerCase() === "closed"
                          ? "text-green-500"
                          : ""
                      }`}
                    >
                      {request.status ?? "-"}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-base ">
                    {formatDate(request?.created_at.split("T")[0])}
                  </td>
                  <td>
                    <button
                      onClick={() => onViewRequestClick(request)}
                      className="flex justify-center rounded-md bg-red-600 px-3 py-1.5 text-base font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {requestData?.count > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      )}
      <Modal
        modalOpen={showRaiseRequestModal}
        handleModalOpen={() => setShowRaiseRequestModal(false)}
      >
        <RaiseRequestForm
          handleModal={setShowRaiseRequestModal}
          options={options}
          fetchData={fetchRequestData}
        />
      </Modal>

      <Modal
        modalOpen={viewRaiseRequestModal}
        handleModalOpen={() => setViewRaiseRequestModal(false)}
      >
        <ViewRaiseRequest requestData={selectedRequest} />
      </Modal>
    </div>
  );
};

export default withAuth(RaiseRequestPage);
