import {
  StartGameModalProps,
  UpdateInGameNameResponse,
} from "@/app/types/CommonComponent.types";
import Link from "next/link";
import UpdateInGameNameForm from "../updateInGameNameForm";
import { useState } from "react";
const StartGameModal: React.FC<StartGameModalProps> = ({
  game_link,
  room_password,
  room_id,
  bookingId,
  tournamentName,
  inGameName,
  showUpdateInGameNameForm = false,
  onSuccess,
}) => {
  const [updatedInGameName, setUpdatedInGameName] = useState(inGameName);
  const [updatedRoomId, setUpdatedRoomId] = useState(room_id);
  const [updatedRoomPassword, setUpdatedRoomPassword] = useState(room_password);
  const [updatedGameLink, setUpdatedGameLink] = useState(game_link);
  const onInGameNameUpdate = (data: UpdateInGameNameResponse) => {
    if (onSuccess && typeof onSuccess === "function") {
      onSuccess();
    }
    setUpdatedInGameName(data.in_game_name);
    setUpdatedRoomId(data.room_id);
    setUpdatedRoomPassword(data.room_password);
    setUpdatedGameLink(data.game_link);
  };
  return (
    <div className="px-4">
      <h3 className="text-3xl font-bold mb-6 text-white">
        Game Joining Details
      </h3>
      {!updatedInGameName && showUpdateInGameNameForm && (
        <div className="mb-6">
          <UpdateInGameNameForm
            bookingId={bookingId as string}
            onSuccess={onInGameNameUpdate}
            tournamentName={tournamentName as string}
          />
        </div>
      )}
      <div className="mb-6 space-y-1">
        {showUpdateInGameNameForm && (
          <div className="grid grid-cols-2 gap-x-16">
            <div>
              <span className="font-semibold text-lg text-white">
                In Game Name
              </span>
            </div>
            <div>
              <span className="font-medium text-lg text-white">
                {updatedInGameName}
              </span>
            </div>
          </div>
        )}
        <div className="grid grid-cols-2 gap-x-16">
          <div>
            <span className="font-semibold text-lg text-white">Room ID</span>
          </div>
          <div>
            <span className="font-medium text-lg text-white">
              {updatedRoomId}
            </span>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-16">
          <div>
            <span className="font-semibold text-lg text-white">
              Room Password
            </span>
          </div>
          <div>
            <span className="font-medium text-lg text-white">
              {updatedRoomPassword}
            </span>
          </div>
        </div>
      </div>
      <div className="mb-3">
        <p className="text-sm text-white">
          <span className="font-semibold">Notes:</span> Joining details will be
          available before the start of the tournament.
        </p>
      </div>

      <div className="flex gap-4">
        <Link
          href={updatedGameLink ?? ""}
          target="_blank"
          className={`flex justify-center w-full rounded-md bg-red-600 px-4 py-2 text-base font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 ${
            !updatedGameLink ? "cursor-not-allowed opacity-70" : ""
          }`}
          onClick={(e) => {
            if (!updatedGameLink) {
              e.preventDefault();
            }
          }}
        >
          Click to Watch Live
        </Link>
      </div>
    </div>
  );
};

export default StartGameModal;
