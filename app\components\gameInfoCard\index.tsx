import { GameInfoCardProps } from "@/app/types/CommonComponent.types";
import React from "react";
import PlayerProgressBar from "../PlayerProgressBar/index";
import Loader from "../common/Loader";
import Link from "next/link";

const GameInfoCard: React.FC<GameInfoCardProps> = ({
  tournamentDetails,
  onJoinNow,
  showJoinButton = true,
  isLoading = false,
}) => {

  return (
    <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white min-w-[850px]">
      {isLoading ? (
        <div className="flex justify-center items-center h-[312px]">
          <Loader />
        </div>
      ) : (
        <>
          <div className="flex p-6 mb-6">
            <div className="flex-shrink-0 relative w-[231px] h-[264px]">
              <img
                src={tournamentDetails?.image ?? ""}
                alt="Game Image"
                className="object-cover rounded-[10px] w-full h-full"
              />
              {showJoinButton && (
                <Link
                  href={tournamentDetails?.created_by?.youtube_link ?? ""}
                  target="_blank"
                  className="flex gap-5 mt-4 items-center w-full bg-[#c9ff88] text-[#070b28] px-4 py-1.5 rounded-[10px] text-base font-semibold"
                >
                  <div>
                    <img
                      src={
                        tournamentDetails?.created_by?.image ||
                        "/icons/youtube.svg"
                      }
                      alt="user"
                      width={60}
                      height={50}
                    />
                  </div>
                  <div className="flex flex-col items-center">
                    <span>Created By:</span>
                    <span>{tournamentDetails?.created_by?.name}</span>
                  </div>
                </Link>
              )}
            </div>

            <div className="ml-6 flex-1 space-y-3">
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <p className="text-white text-base">Date</p>
                  <p className="font-bold text-white text-base">
                    {tournamentDetails?.date}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-white text-base">Token ID</p>
                  <p className="font-bold text-white text-base">
                    {tournamentDetails?.tournament_id}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-white text-base">Join Using</p>
                  <div className="flex items-center">
                    <span className="ml-2 font-bold text-base">
                      ₹ {tournamentDetails?.payment?.final_amount?.toLocaleString(
                    "en-IN"
                  )}
                    </span>
                  </div>
                </div>
              </div>
              <div className="bg-[#c9ff88] grid grid-cols-2 py-2 px-2.5">
                <div className="space-y-1">
                  <p className="text-black text-base">Map</p>
                  <p className="font-bold text-black text-base">
                    {tournamentDetails?.map}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-black text-base">Mode</p>
                  <p className="font-bold text-black text-base">
                    {tournamentDetails?.mode}
                  </p>
                </div>
              </div>
              {tournamentDetails?.per_kill_prize ? (
                <div className="grid grid-cols-2  gap-4 mt-4">
                  <div className="space-y-1">
                    <p className="text-white text-base">Prize Pool</p>
                    <div className="flex items-center">
                      <span className="ml-2 font-bold text-base">
                        ₹{tournamentDetails?.prize_pool.toLocaleString("en-IN")}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-white text-base">Per Kill Prize</p>
                    <p className="font-bold text-white text-base">
                      ₹
                      {tournamentDetails?.per_kill_prize.toLocaleString(
                        "en-IN"
                      )}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
                  <div className="space-y-1">
                    <p className="text-white text-base">Prize Pool</p>
                    <div className="flex items-center">
                      <span className="ml-2 font-bold text-base">
                        ₹
                        {tournamentDetails?.prize_pool?.toLocaleString("en-IN")}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-white text-base">1st Prize</p>
                    <p className="font-bold text-white text-base">
                      ₹{tournamentDetails?.first_prize?.toLocaleString("en-IN")}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-white text-base">2nd Prize</p>
                    <p className="font-bold text-white text-base">
                      ₹
                      {tournamentDetails?.second_prize?.toLocaleString("en-IN")}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-white text-base">3rd Prize</p>
                    <p className="font-bold text-white text-base">
                      ₹{tournamentDetails?.third_prize?.toLocaleString("en-IN")}
                    </p>
                  </div>
                </div>
              )}

              <PlayerProgressBar
                bookingsCount={tournamentDetails?.bookings_count ?? 0}
                maxPlayers={tournamentDetails?.max_players ?? 0}
              />
              {showJoinButton && onJoinNow && (
                <div className="w-full flex justify-center">
                  <button
                    className="bg-[#c9ff88] text-[#070b28] px-6 py-3 rounded-[10px] text-base font-semibold absolute -bottom-5 w-[60%] mx-auto"
                    onClick={onJoinNow}
                  >
                    JOIN NOW
                  </button>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default GameInfoCard;
