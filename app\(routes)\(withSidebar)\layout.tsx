"use client";
import Sidebar from "@/app/components/sidebar";
import { useSidebar } from "@/app/context/SidebarContext";

const SidebarLayout = ({ children }: { children: React.ReactNode }) => {
  const { isExpanded } = useSidebar();
  return (
    <div className="flex">
      <Sidebar />
      <main
        className={`flex-1 ${
          isExpanded ? "pl-[320px]" : "pl-[100px]"
        } bg-gradient-to-r from-[#122531] via-[#121f28] to-[#1a2c38]`}
      >
        {children}
      </main>
    </div>
  );
};
export default SidebarLayout;
